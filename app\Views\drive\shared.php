<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- Shared Files Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-share mr-2"></i><?= tr("Shared with me") ?>
    </h1>
</div>

<!-- Shared Directories -->
<?php if (!empty($sharedDirectories)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary"><?= tr("Shared Directories") ?></h6>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($sharedDirectories as $directory): ?>
                <div class="col-md-3 col-sm-4 col-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center p-3">
                            <i class="fas fa-folder fa-3x text-warning mb-2"></i>
                            <h6 class="card-title mb-1 text-truncate" title="<?= esc($directory->name) ?>">
                                <?= esc($directory->name) ?>
                            </h6>
                            <small class="text-muted">
                                <?= tr("Shared by") ?> <?= esc($directory->user->name) ?>
                            </small>
                            <br>
                            <small class="text-muted">
                                <?php 
                                $share = $directory->shares->where('shared_with_user_id', auth()->id)->first();
                                echo $share ? $share->permission_label : '';
                                ?>
                            </small>
                        </div>
                        <div class="card-footer p-2">
                            <div class="btn-group btn-group-sm w-100">
                                <a href="<?= base_url("drive/{$directory->id}") ?>" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-folder-open"></i> <?= tr("Open") ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Shared Files -->
<?php if (!empty($sharedFiles)): ?>
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary"><?= tr("Shared Files") ?></h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th><?= tr("Name") ?></th>
                        <th><?= tr("Size") ?></th>
                        <th><?= tr("Shared by") ?></th>
                        <th><?= tr("Permission") ?></th>
                        <th><?= tr("Shared on") ?></th>
                        <th><?= tr("Actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sharedFiles as $file): ?>
                        <?php $share = $file->shares->where('shared_with_user_id', auth()->id)->first(); ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?= _render_icon($file, 'fa-lg', 'mr-2') ?>
                                    <div>
                                        <div class="font-weight-bold"><?= esc($file->name) ?></div>
                                        <small class="text-muted"><?= esc($file->directory->name) ?></small>
                                    </div>
                                </div>
                            </td>
                            <td><?= $file->human_size ?></td>
                            <td><?= esc($file->user->name) ?></td>
                            <td>
                                <span class="badge badge-<?= $share && $share->permission_level === 'EDIT' ? 'warning' : 'info' ?>">
                                    <?= $share ? $share->permission_label : '' ?>
                                </span>
                            </td>
                            <td><?= $share ? $share->created_at->format('M d, Y') : '' ?></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="<?= $file->getUrl() ?>" target="_blank" 
                                       class="btn btn-outline-primary btn-sm" title="<?= tr("View") ?>">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= $file->getDownloadUrl() ?>" 
                                       class="btn btn-outline-success btn-sm" title="<?= tr("Download") ?>">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <?php if ($file->canEditWithOnlyOffice() && $share && $share->permission_level === 'EDIT'): ?>
                                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                                onclick="editWithOnlyOffice('<?= $file->id ?>')" title="<?= tr("Edit") ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Empty State -->
<?php if (empty($sharedDirectories) && empty($sharedFiles)): ?>
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-share fa-4x text-muted mb-3"></i>
        <h5 class="text-muted"><?= tr("No shared items") ?></h5>
        <p class="text-muted"><?= tr("Files and folders shared with you will appear here") ?></p>
    </div>
</div>
<?php endif; ?>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script src="<?= base_url('assets/js/drive.js') ?>"></script>
<?= $this->endSection() ?>
