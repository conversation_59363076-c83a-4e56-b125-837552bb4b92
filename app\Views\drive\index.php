<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- Drive Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-folder mr-2"></i><?= tr("Drive") ?>
    </h1>
    <div class="dropdown">
        <button class="btn btn-primary dropdown-toggle" type="button" id="actionDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-plus mr-1"></i><?= tr("New") ?>
        </button>
        <div class="dropdown-menu" aria-labelledby="actionDropdown">
            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#createFolderModal">
                <i class="fas fa-folder-plus mr-2"></i><?= tr("Create Folder") ?>
            </a>
            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#uploadModal">
                <i class="fas fa-upload mr-2"></i><?= tr("Upload File") ?>
            </a>
            <a class="dropdown-item" href="#"  onclick="createFile('word')">
                <i class="fas fa-file-word text-blue-700 mr-2"></i><?= tr("Create Word Document") ?>
            </a>
            <a class="dropdown-item" href="#"  onclick="createFile('excel')">
                <i class="fas fa-file-excel text-green-700 mr-2"></i><?= tr("Create Excel Document") ?>
            </a>
            <a class="dropdown-item" href="#"  onclick="createFile('powerpoint')">
                <i class="fas fa-file-powerpoint text-danger-700 mr-2"></i><?= tr("Create Powerpoint Document") ?>
            </a>
            <a class="dropdown-item" href="#"  onclick="createFile('file')">
                <i class="fas fa-file text-gray-300 mr-2"></i><?= tr("Create File") ?>
            </a>
            
            
        </div>
    </div>
</div>

<!-- Storage Usage -->
<div class="row ">
    <div class="col-12">
        <div class="alert alert-light shadow-sm">
            <div class="" style="padding: 10px;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1"><?= tr("Storage Usage") ?></h6>
                        <small class="text-muted">
                            <?= number_format($storageUsed / (1024*1024), 2) ?> MB of 
                            <?= number_format($storageLimit / (1024*1024*1024), 2) ?> GB used
                        </small>
                    </div>
                    <div class="progress" style="width: 200px; height: 8px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: <?= min(100, ($storageUsed / $storageLimit) * 100) ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb Navigation -->
<?php if (!empty($breadcrumb)): ?>
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <?php foreach ($breadcrumb as $index => $crumb): ?>
            <?php if ($index === count($breadcrumb) - 1): ?>
                <li class="breadcrumb-item active" aria-current="page"><?= esc($crumb['name']) ?></li>
            <?php else: ?>
                <li class="breadcrumb-item">
                    <a href="<?= base_url("drive/{$crumb['id']}") ?>"><?= esc($crumb['name']) ?></a>
                </li>
            <?php endif; ?>
        <?php endforeach; ?>
    </ol>
</nav>
<?php endif; ?>

<!-- Drive Content -->
<div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Files and Folders") ?></h6>
                <div class="btn-group btn-group-sm">
                    <!-- <button type="button" class="btn btn-outline-secondary active" id="viewGrid">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="viewList">
                        <i class="fas fa-list"></i>
                    </button> -->
                </div>
            </div>
            <div class="card-body">
                <!-- Directories -->
                <?php if (!empty($directories)): ?>
                    <div class="row" id="directoriesContainer">
                        <?php foreach ($directories as $directory): ?>
                            <div class="col-md-3 col-sm-4 col-6 mb-3">
                                <div class="card h-100 directory-item" data-id="<?= $directory->id ?>">
                                    <div class="card-body text-center p-3">
                                        <i class="fas fa-folder fa-3x text-warning mb-2"></i>
                                        <h6 class="card-title mb-1 text-truncate" title="<?= esc($directory->name) ?>">
                                            <?= esc($directory->name) ?>
                                        </h6>
                                        <small class="text-muted">
                                            <?= $directory->files()->count() ?> <?= tr("files") ?>
                                        </small>
                                    </div>
                                    <div class="card-footer p-2">
                                        <div class="btn-group btn-group-sm w-100 ">
                                            <a href="<?= base_url("drive/{$directory->id}") ?>" 
                                               class="btn text-primary btn-sm">
                                                <i class="fas fa-folder-open"></i>
                                            </a>
                                            <button type="button" class="btn text-secondary btn-sm" 
                                                    onclick="renameDirectory('<?= $directory->id ?>', '<?= esc($directory->name) ?>')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn text-info btn-sm" 
                                                    onclick="shareDirectory('<?= $directory->id ?>')">
                                                <i class="fas fa-share"></i>
                                            </button>
                                            <button type="button" class="btn text-danger btn-sm" 
                                                    onclick="deleteDirectory('<?= $directory->id ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- Files -->
                <?php if (!empty($files)): ?>
                    <div class="row" id="filesContainer">
                        <?php foreach ($files as $file): ?>
                            <div class="col-md-3 col-sm-4 col-6 mb-3">
                                <div class="card h-100 file-item" data-id="<?= $file->id ?>">
                                    <div class="card-body text-center p-3">
                                        <?= _render_icon($file) ?>
                                        <h6 class="card-title mb-1 text-truncate" title="<?= esc($file->name) ?>">
                                            <?= esc($file->name) ?>
                                        </h6>
                                        <small class="text-muted">
                                            <?= $file->human_size ?>
                                        </small>
                                    </div>
                                    <div class="card-footer p-2">
                                        <div class="btn-group btn-group-sm w-100">
                                            <a href="<?= $file->getUrl() ?>" target="_blank" 
                                               class="btn text-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= $file->getDownloadUrl() ?>" 
                                               class="btn text-success btn-sm">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <?php if ($file->canEditWithOnlyOffice()): ?>
                                                <button type="button" class="btn text-warning btn-sm" 
                                                        onclick="editWithOnlyOffice('<?= $file->id ?>')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn text-info btn-sm" 
                                                    onclick="shareFile('<?= $file->id ?>')">
                                                <i class="fas fa-share"></i>
                                            </button>
                                            <button type="button" class="btn text-danger btn-sm" 
                                                    onclick="deleteFile('<?= $file->id ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- Empty State -->
                <?php if (empty($directories) && empty($files)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted"><?= tr("This folder is empty") ?></h5>
                        <p class="text-muted"><?= tr("Upload files or create folders to get started") ?></p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#uploadModal">
                            <i class="fas fa-upload mr-1"></i><?= tr("Upload Files") ?>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Recent Activity -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Recent Activity") ?></h6>
            </div>
            <div class="card-body">
                <?php if (!empty($recentActivity)): ?>
                    <?php foreach ($recentActivity as $activity): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <i class="fas fa-<?= $activity->action === 'UPLOAD' ? 'upload' : 
                                    ($activity->action === 'DOWNLOAD' ? 'download' : 'file') ?> text-muted"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small font-weight-bold">
                                    <?= $activity->action_label ?>
                                    <?php if ($activity->file): ?>
                                        <?= esc($activity->file->name) ?>
                                    <?php elseif ($activity->directory): ?>
                                        <?= esc($activity->directory->name) ?>
                                    <?php endif; ?>
                                </div>
                                <div class="small text-muted">
                                    <?= $activity->created_at->diffForHumans() ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted text-center"><?= tr("No recent activity") ?></p>
                <?php endif; ?>
            </div>
        </div>

       
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= tr("Upload Files") ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form enctype="multipart/form-data" class="ajax" action="<?= base_url("drive/files/upload") ?>" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label><?= tr("Select Files") ?></label>
                        <?= storage()->input(["name"=>"file","required"=>true]) ?>
                        <input type="hidden" name="directory_id" value="<?= $currentDirectory->id ?>">
                    </div>
                    <div class="progress d-none" id="uploadProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>  
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= tr("Cancel") ?></button>
                    <button type="submit" class="btn btn-primary"><?= tr("Upload") ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= tr("Create New Folder") ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="createFolderForm" class="ajax" action="<?= base_url("drive/directories/create") ?>" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label><?= tr("Folder Name") ?></label>
                        <input type="text" class="form-control" name="name" required>
                        <input type="hidden" name="parent_id" value="<?= $currentDirectory->id ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fas fa-times mr-1"></i> <?= tr("Cancel") ?></button>
                    <button type="submit" class="btn btn-primary"><i class="fas fa-save mr-1"></i> <?= tr("Save") ?></button>
                </div>
            </form>
        </div>
    </div>
</div>


<div class="modal fade" id="createFileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= tr("Create New File") ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="createFileForm" class="ajax" action="<?= base_url("drive/files/create") ?>" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label><?= tr("File Name") ?></label>
                        <input type="text" class="form-control" name="name" required>
                        <input type="hidden" name="directory_id" value="<?= $currentDirectory->id ?>">
                        <input type="hidden" name="type" value="">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fas fa-times mr-1"></i> <?= tr("Cancel") ?></button>
                    <button type="submit" class="btn btn-primary"><i class="fas fa-save mr-1"></i> <?= tr("Save") ?></button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
    function createFile(type) {
        $('#createFileModal').modal('show');
        $('#createFileModal input[name="type"]').val(type);
        $('#createFileModal input[name="directory_id"]').val(<?= $currentDirectory->id ?>);


    }
</script>


<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script src="<?= base_url('assets/js/drive.js') ?>"></script>
<?= $this->endSection() ?>
