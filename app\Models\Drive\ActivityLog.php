<?php

namespace App\Models\Drive;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;


class ActivityLog extends Model
{
    protected $table = 'activity_logs';
    
    // UUID primary key
    protected $keyType = 'string';
    public $incrementing = false;
    
    protected $fillable = [
        'id',
        'user_id',
        'file_id',
        'directory_id',
        'action',
        'description',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'created_at' => 'datetime'
    ];

    const ACTION_UPLOAD = 'UPLOAD';
    const ACTION_DOWNLOAD = 'DOWNLOAD';
    const ACTION_VIEW = 'VIEW';
    const ACTION_EDIT = 'EDIT';
    const ACTION_DELETE = 'DELETE';
    const ACTION_MOVE = 'MOVE';
    const ACTION_COPY = 'COPY';
    const ACTION_SHARE = 'SHARE';
    const ACTION_RENAME = 'RENAME';
    const ACTION_CREATE = 'CREATE';



    /**
     * Get the user who performed the action
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the file involved in the action
     */
    public function file()
    {
        return $this->belongsTo(File::class, 'file_id');
    }

    /**
     * Get the directory involved in the action
     */
    public function directory()
    {
        return $this->belongsTo(Directory::class, 'directory_id');
    }

    /**
     * Scope to get logs for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get logs for a specific file
     */
    public function scopeForFile($query, $fileId)
    {
        return $query->where('file_id', $fileId);
    }

    /**
     * Scope to get logs for a specific directory
     */
    public function scopeForDirectory($query, $directoryId)
    {
        return $query->where('directory_id', $directoryId);
    }

    /**
     * Scope to get logs by action type
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to get recent logs
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', \Carbon\Carbon::now()->subDays($days));
    }

    /**
     * Get the target item (file or directory)
     */
    public function getTargetItem()
    {
        if ($this->file_id) {
            return $this->file;
        }
        
        if ($this->directory_id) {
            return $this->directory;
        }
        
        return null;
    }

    /**
     * Get target type
     */
    public function getTargetType()
    {
        if ($this->file_id) {
            return 'file';
        }
        
        if ($this->directory_id) {
            return 'directory';
        }
        
        return null;
    }

    /**
     * Get human readable action description
     */
    public function getActionLabelAttribute()
    {
        switch ($this->action) {
            case self::ACTION_UPLOAD:
                return 'Uploaded';
            case self::ACTION_DOWNLOAD:
                return 'Downloaded';
            case self::ACTION_VIEW:
                return 'Viewed';
            case self::ACTION_EDIT:
                return 'Edited';
            case self::ACTION_DELETE:
                return 'Deleted';
            case self::ACTION_MOVE:
                return 'Moved';
            case self::ACTION_COPY:
                return 'Copied';
            case self::ACTION_SHARE:
                return 'Shared';
            case self::ACTION_RENAME:
                return 'Renamed';
            case self::ACTION_CREATE:
                return 'Created';
            default:
                return $this->action;
        }
    }

    /**
     * Log an activity
     */
    public static function logActivity($action, $userId = null, $fileId = null, $directoryId = null, $description = null, $ipAddress = null, $userAgent = null)
    {
        // Get current request info if not provided
        if (!$ipAddress) {
            $ipAddress = request()->getIPAddress();
        }
        
        if (!$userAgent) {
            $userAgent = request()->getUserAgent()->getAgentString();
        }
        
        // Get current user if not provided
        if (!$userId && auth()) {
            $userId = auth()->id;
        }

        return self::create([
            'id' => uuid(),
            'action' => $action,
            'user_id' => $userId,
            'file_id' => $fileId,
            'directory_id' => $directoryId,
            'description' => $description,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent
        ]);
    }

    /**
     * Log file upload
     */
    public static function logFileUpload($fileId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_UPLOAD, $userId, $fileId, null, $description);
    }

    /**
     * Log file download
     */
    public static function logFileDownload($fileId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_DOWNLOAD, $userId, $fileId, null, $description);
    }

    /**
     * Log file view
     */
    public static function logFileView($fileId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_VIEW, $userId, $fileId, null, $description);
    }

    /**
     * Log file edit
     */
    public static function logFileEdit($fileId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_EDIT, $userId, $fileId, null, $description);
    }

    /**
     * Log file share
     */
    public static function logFileShare($fileId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_SHARE, $userId, $fileId, null, $description);
    }

    /**
     * Log directory creation
     */
    public static function logDirectoryCreate($directoryId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_CREATE, $userId, null, $directoryId, $description);
    }

    /**
     * Log file creation
     */
    public static function logFileCreate($fileId, $userId = null, $description = null)
    {
        return self::logActivity(self::ACTION_CREATE, $userId, $fileId, null, $description);
    }
}
