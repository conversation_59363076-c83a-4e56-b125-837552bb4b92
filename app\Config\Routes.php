<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Public routes (no authentication required)
$routes->get('/', 'Home::index');
$routes->get('/job/(:any)', 'Home::job_details/$1');
$routes->get('/proceed/(:any)', 'Home::proceed/$1');
$routes->get('/profile_preview', 'Home::profile_preview');
$routes->post('/confirm_application', 'Home::confirm_application');
$routes->get('/success', 'Home::application_success');
$routes->match(['get', 'post'], '/get_cities/(:any)', 'Home::get_cities/$1');
$routes->match(['get', 'post'], 'home/get_cities/(:any)', 'Home::get_cities/$1');

// Auth routes (no authentication required - these handle authentication)
$routes->group('auth', [], function($routes) {
    $routes->match(['get', 'post'], 'login', 'Auth::login');
    $routes->match(['get', 'post'], 'register', 'Auth::register');
    $routes->match(['get', 'post'], 'otp_login', 'Auth::otp_login');
    $routes->match(['get', 'post'], 'otp', 'Auth::otp');
    $routes->match(['get', 'post'], 'verify_otp', 'Auth::verify_otp');
    $routes->match(['get', 'post'], 'resend_otp', 'Auth::resend_otp');
    $routes->match(['get', 'post'], 'forgot_password', 'Auth::forgot_password');
    $routes->match(['get', 'post'], 'reset_password', 'Auth::reset_password');
    $routes->match(['get', 'post'], 'reset_password/(:any)', 'Auth::reset_password/$1');
    $routes->get('logout', 'Auth::logout');
    $routes->get('profile', 'Auth::profile');
    $routes->match(['get', 'post'], 'change_password', 'Auth::change_password');
    $routes->match(['get', 'post'], '2fa', 'Auth::two_factor');
    $routes->match(['get', 'post'], '2fa/enable', 'Auth::enable_2fa');
    $routes->match(['get', 'post'], '2fa/disable', 'Auth::disable_2fa');
    $routes->get('verify_email/(:any)', 'Auth::verify_email/$1');
    $routes->match(['get', 'post'], 'verify_2fa', 'Auth::verify_2fa');
});

// Dashboard (protected)
$routes->get('dashboard', 'Dashboard::index', ['filter' => 'auth']);


// Data export routes (protected)
$routes->post('data/export/(:any)', 'Data::export/$1', ['filter' => 'auth']);

// File management routes (protected)
$routes->get('files/(:any)', 'Files::index/$1');

// Profile routes (protected)
$routes->group('profile', function($routes) {
    $routes->get('/', 'ProfileController::index');
    $routes->get('login', 'ProfileController::login');
    $routes->post('login', 'ProfileController::login');
    $routes->get('otp', 'ProfileController::otp');
    $routes->post('otp', 'ProfileController::otp');
    $routes->post('resend_otp', 'ProfileController::resend_otp');
    $routes->match(['get', 'post'], 'logout', 'ProfileController::logout');
    
    // Profile Management
    $routes->get('edit', 'ProfileController::edit');
    $routes->post('edit', 'ProfileController::edit');
    $routes->post('upload_image', 'ProfileController::upload_image');
    
    // Profile Components
    $routes->post('create_qualification', 'ProfileController::create_qualification');
    $routes->get('delete_qualification/(:any)', 'ProfileController::delete_qualification/$1');
    $routes->post('create_experience', 'ProfileController::create_experience');
    $routes->get('delete_experience/(:any)', 'ProfileController::delete_experience/$1');
    $routes->post('create_cert', 'ProfileController::create_cert');
    $routes->get('delete_cert/(:any)', 'ProfileController::delete_cert/$1');
});

// Roles management (protected)
$routes->group('roles', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Roles::index');
    $routes->post('/', 'Roles::index');
    $routes->get('create', 'Roles::create');
    $routes->post('create', 'Roles::create');
    $routes->get('update/(:num)', 'Roles::update/$1');
    $routes->post('update/(:num)', 'Roles::update/$1');
    $routes->get('delete/(:num)', 'Roles::delete/$1');
});

// Users management (protected)
$routes->group('users', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Users::index');
    $routes->get('new', 'Users::new');
    $routes->post('new', 'Users::new');
    $routes->get('view/(:num)', 'Users::view/$1');
    $routes->post('view/(:num)', 'Users::view/$1');
    $routes->get('reset_password/(:num)', 'Users::reset_password/$1');
    $routes->get('delete/(:num)', 'Users::delete/$1');
    
    // Profile and 2FA routes
    $routes->get('profile', 'Users::profile');
    $routes->match(['get', 'post'], 'setup_2fa', 'Users::setup_2fa');
    $routes->post('confirm_2fa', 'Users::confirm_2fa');
    $routes->get('disable_2fa', 'Users::disable_2fa');
    $routes->get('send_reset_password', 'Users::send_reset_password');
});

$routes->group('settings', ['filter' => 'auth'], function($routes) {
    $routes->match(['get', 'post'], '/', 'Settings::index');
});

// Language Management routes (protected)
$routes->group('language-manager', ['filter' => 'auth'], function($routes) {
    $routes->match(['get', 'post'], '/', 'LanguageManager::index');
    $routes->match(['get', 'post'], 'datatable', 'LanguageManager::datatable');
    $routes->post('save', 'LanguageManager::save');
});

// Logs management (protected)
$routes->group('logs', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Logs::index');
    $routes->match(['get', 'post'], 'datatable', 'Logs::datatable');
});

// OnlyOffice callbacks (no auth required - called by OnlyOffice server)
$routes->group('drive/onlyoffice', [], function($routes) {
    $routes->post('callback/(:any)', 'Drive\OnlyOfficeController::callback/$1');
    $routes->get('callback/(:any)', 'Drive\OnlyOfficeController::callback/$1');
    $routes->options('callback/(:any)', 'Drive\OnlyOfficeController::callback/$1');
    $routes->get('test-callback', 'Drive\OnlyOfficeController::testCallback');
    $routes->options('test-callback', 'Drive\OnlyOfficeController::testCallback');
    $routes->get('download/(:any)', 'Drive\OnlyOfficeController::download/$1');
});
// Drive System routes (protected)
$routes->group('drive', ['filter' => 'auth'], function($routes) {
    // Main drive interface
    

    // Directory operations
    $routes->group('directories', [], function($routes) {
        $routes->post('create', 'Drive\DirectoryController::create');
        $routes->post('(:any)/rename', 'Drive\DirectoryController::rename/$1');
        $routes->post('(:any)/delete', 'Drive\DirectoryController::delete/$1');
        $routes->post('(:any)/move', 'Drive\DirectoryController::move/$1');
        $routes->get('(:any)/contents', 'Drive\DirectoryController::contents/$1');
    });

    // File operations
    $routes->group('files', [], function($routes) {
        $routes->post('upload', 'Drive\FileController::upload');
        $routes->post('create', 'Drive\FileController::create');
        $routes->get('(:any)/download', 'Drive\FileController::download/$1');
        $routes->get('(:any)/view', 'Drive\FileController::view/$1');
        $routes->post('(:any)/delete', 'Drive\FileController::delete/$1');
        $routes->post('(:any)/rename', 'Drive\FileController::rename/$1');
        $routes->post('(:any)/move', 'Drive\FileController::move/$1');
        $routes->post('(:any)/copy', 'Drive\FileController::copy/$1');
        $routes->get('(:any)/versions/(:any)/download', 'Drive\FileController::downloadVersion/$1/$2');
    });

    // Sharing operations
    $routes->group('share', [], function($routes) {
        $routes->post('file/(:any)', 'Drive\ShareController::shareFile/$1');
        $routes->post('directory/(:any)', 'Drive\ShareController::shareDirectory/$1');
        $routes->post('(:any)/revoke', 'Drive\ShareController::revokeShare/$1');
        $routes->post('(:any)/update', 'Drive\ShareController::updateShare/$1');
        $routes->get('list', 'Drive\ShareController::getShares');
    });

    



    // OnlyOffice integration (within drive group)
    $routes->group('onlyoffice', [], function($routes) {
        $routes->get('edit/(:any)', 'Drive\OnlyOfficeController::edit/$1');
        $routes->get('download/(:any)', 'Drive\OnlyOfficeController::download/$1');
        $routes->get('config', 'Drive\OnlyOfficeController::config');
        $routes->post('config/save', 'Drive\OnlyOfficeController::saveConfig');
        $routes->post('test-connection', 'Drive\OnlyOfficeController::testConnection');
        $routes->get('debug/(:any)', 'Drive\OnlyOfficeController::debugConfig/$1');
    });

    $routes->get('/', 'Drive\DriveController::index');
    
    $routes->get('shared', 'Drive\DriveController::shared');
    $routes->get('recent', 'Drive\DriveController::recent');
    $routes->post('search', 'Drive\DriveController::search');

    $routes->get('(:any)', 'Drive\DriveController::index/$1');
});



// Public shared access (no authentication required)
$routes->get('drive/shared/(:any)', 'Drive\ShareController::accessShared/$1');

// Test/notification routes (special access)
$routes->group('tt', [], function($routes) {
    $routes->get('sms', 'Tt::sms');
    $routes->get('email', 'Tt::email');
});

