<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;


use App\Models\Job;
use App\Models\JobInterviewer;
use App\Models\Application;
use App\Models\ApplicationInterviewMark;

use Illuminate\Database\Capsule\Manager as DB;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    public $d=[
        "page"=>[
            
            "title"=>""
        ],
        "nav"=>[
            "reload"=>true,
        ]
    ];


    public $days = [
        "01", "02", "03", "04", "05", "06", "07", "08", "09", 
        "10", "11", "12", "13", "14", "15", "16", "17", "18", 
        "19", "20", "21", "22", "23", "24", "25", "26", "27", 
        "28", "29", "30", "31"
    ];

    public $months = [
        "01", "02", "03", "04", "05", "06", "07", "08", "09", 
        "10", "11", "12"
    ];

    public $years = [];

    public $majors = [

        "1"
    ];


    public $genders=[

        "Male",
        "Female",
    ];

    public $rights=[

        "drive"       =>"drive",
        "storage"       =>"storage",
        "admins"       =>"admins",
       
  
        "settings"=>"settings"
 
    ];




    public $document_statuses=[
        "pending"=>"قيد المراجعة",
        "approved"=>"موافق عليه",
        "rejected"=>"مرفوض",
    ]; 

    public $document_types=[
        'private'=>"خاص",
        'public'=>"عام",
    ];

    public $document_categories=[
        'secret'=>"السري",
        'public'=>"العام",
        'internal'=>"الداخلي",
        'external'=>"الخارجي",
        'internal'=>"الداخلي",
    ];


    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = [
        "App_helpers",
        "url",
        "model",
        "validator",
        "settings",
        "display",
        "hook",
        "form",
        "notify",
        "template",
        "files",
    
    ];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();
        
         new \Config\Eloquent();

         hooks()->do("init");


        $this->years = range(date("Y")-50 ,date("Y"), );


         navigation()       
    ->add([
        "name" => "dashboard",
        "title" => tr("Dashboard"),
        "icon" => "icon-home4",
        "role" => "",
        "badge" => 0,
        "order" => 0,
        "links" => []
    ])
    ->add_sub("dashboard", [
        "title" => tr("Dashboard"),
        "attr" => base_url("dashboard"),
        "type" => "link",
        "role" => "",
        "name"=>"",
        "order" => 0,
        "badge" => 0,
        "icon" => "",
    ])

    

    ->add([
        "name" => "drive",
        "title" => tr("My Drive"),
        "icon" => "icon-folder",
        "role" => "",
        "badge" => 0,
        "order" => 2,
        "links" => []
    ])
    ->add_sub("drive", [
        "title" => tr("My Drive"),
        "attr" => base_url("drive"),
        "type" => "link",
        "role" => "",
        "name" => "",
        "order" => 0,
        "badge" => 0,
        "icon" => "icon-folder",
    ])
    ->add([
        "name" => "shared",
        "title" => tr("Shared (Soon)"),
        "icon" => "icon-share",
        "role" => "",
        "badge" => 0,
        "order" => 2,
        "links" => []
    ])
    ->add_sub("shared", [
        "title" => tr("Shared"),
        "attr" => "#",
        "type" => "link",
        "role" => "",
        "name" => "",
        "order" => 1,
        "badge" => 0,
        "icon" => "icon-share",
    ])

    ->add([
        "name" => "calendar",
        "title" => tr("Calendar (Soon)"),
        "icon" => "icon-calendar",
        "role" => "",
        "badge" => 0,
        "order" => 2,
        "links" => []
    ])
    ->add_sub("calendar", [
        "title" => tr("Calendar"),
        "attr" => "#",
        "type" => "link",
        "role" => "",
        "name" => "",
        "order" => 1,
        "badge" => 0,
        "icon" => "icon-calendar",
    ])
    ->add([
        "name" => "tasks",
        "title" => tr("Tasks (Soon)"),
        "icon" => "fas fa-tasks",
        "role" => "",
        "badge" => 0,
        "order" => 2,
        "links" => []
    ])
    ->add_sub("tasks", [
        "title" => tr("Tasks"),
        "attr" => "#",
        "type" => "link",
        "role" => "",
        "name" => "",
        "order" => 1,
        "badge" => 0,
        "icon" => "fas fa-tasks",
    ])


    ->add([
        "name" => "admins",
        "title" => tr("Admins"),
        "icon" => "far fa-users",
        "role" => "admins",
        "badge" => 0,
        "order" => 20,
        "links" => []
    ])
    ->add_sub("admins", [
        "title" => tr("Members"),
        "attr" => base_url("/users"),
        "type" => "link",
        "role" => "",
        "name"=>"",
        "order" => 0,
        "badge" => 0,
        "icon" => "",
    ])->add_sub("admins", [
        "title" => tr("Roles"),
        "attr" => base_url("/roles"),
        "type" => "link",
        "role" => "",
        "name"=>"",
        "order" => 0,
        "badge" => 0,
        "icon" => "",
    ])




    ->add([
        "name" => "settings",
        "title" => tr("Settings"),
        "icon" => "fas fa-cogs",
        "role" => "settings",
        "badge" => 0,
        "order" => 20,
        "links" => []
    ])
    ->add_sub("settings", [
        "title" => tr("Settings"),
        "attr" => base_url("/settings"),
        "type" => "link",
        "role" => "",
        "name"=>"",
        "order" => 0,
        "badge" => 0,
        "icon" => "",
    ])
    ->add_sub("settings", [
        "title" => tr("Language Management"),
        "attr" => base_url("/language-manager"),
        "type" => "link",
        "role" => "",
        "name"=>"",
        "order" => 1,
        "badge" => 0,
        "icon" => "",
    ])
    ->add_sub("settings", [
        "title" => tr("System logs"),
        "attr" => base_url("/logs"),
        "type" => "link",
        "role" => "",
        "name"=>"",
        "order" => 2,
        "badge" => 0,
        "icon" => "",
    ])
    ;




   


    }


}
