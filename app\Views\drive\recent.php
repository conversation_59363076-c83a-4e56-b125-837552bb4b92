<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- Recent Files Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-clock mr-2"></i><?= tr("Recent Files") ?>
    </h1>
</div>

<!-- Recent Files -->
<?php if (!empty($recentFiles)): ?>
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary"><?= tr("Recently Modified Files") ?></h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th><?= tr("Name") ?></th>
                        <th><?= tr("Location") ?></th>
                        <th><?= tr("Size") ?></th>
                        <th><?= tr("Modified") ?></th>
                        <th><?= tr("Actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentFiles as $file): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?= _render_icon($file, 'fa-lg', 'mr-2') ?>
                                    <div>
                                        <div class="font-weight-bold"><?= esc($file->name) ?></div>
                                        <small class="text-muted"><?= $file->mime_type ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="<?= base_url("drive/{$file->directory->id}") ?>" class="text-decoration-none">
                                    <i class="fas fa-folder mr-1 text-warning"></i>
                                    <?= esc($file->directory->name) ?>
                                </a>
                            </td>
                            <td><?= $file->human_size ?></td>
                            <td>
                                <span title="<?= $file->updated_at->format('M d, Y H:i:s') ?>">
                                    <?= $file->updated_at->diffForHumans() ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="<?= $file->getUrl() ?>" target="_blank" 
                                       class="btn btn-outline-primary btn-sm" title="<?= tr("View") ?>">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= $file->getDownloadUrl() ?>" 
                                       class="btn btn-outline-success btn-sm" title="<?= tr("Download") ?>">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <?php if ($file->canEditWithOnlyOffice() && $file->canEdit(auth()->id)): ?>
                                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                                onclick="editWithOnlyOffice('<?= $file->id ?>')" title="<?= tr("Edit") ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    <?php endif; ?>
                                    <?php if ($file->user_id === auth()->id): ?>
                                        <button type="button" class="btn btn-outline-info btn-sm" 
                                                onclick="shareFile('<?= $file->id ?>')" title="<?= tr("Share") ?>">
                                            <i class="fas fa-share"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                onclick="renameFile('<?= $file->id ?>', '<?= esc($file->name) ?>')" title="<?= tr("Rename") ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteFile('<?= $file->id ?>')" title="<?= tr("Delete") ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php else: ?>
<!-- Empty State -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-clock fa-4x text-muted mb-3"></i>
        <h5 class="text-muted"><?= tr("No recent files") ?></h5>
        <p class="text-muted"><?= tr("Files you've recently accessed will appear here") ?></p>
        <a href="<?= base_url('drive') ?>" class="btn btn-primary">
            <i class="fas fa-folder mr-1"></i><?= tr("Go to Drive") ?>
        </a>
    </div>
</div>
<?php endif; ?>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script src="<?= base_url('assets/js/drive.js') ?>"></script>
<?= $this->endSection() ?>
