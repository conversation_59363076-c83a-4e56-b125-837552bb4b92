<?= $this->extend('layouts/public') ?>
<?= $this->section('content') ?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-share mr-2"></i><?= tr("Shared Item") ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($shareType === 'file'): ?>
                        <!-- File Share -->
                        <div class="text-center mb-4">
                            <?php if ($sharedItem->isImage()): ?>
                                <img src="<?= $sharedItem->getUrl() ?>" class="img-fluid mb-3" 
                                     style="max-height: 300px; object-fit: contain;" 
                                     alt="<?= esc($sharedItem->name) ?>">
                            <?php else: ?>
                                <?= _render_icon($sharedItem, 'fa-4x', 'mb-3') ?>
                            <?php endif; ?>
                            <h4><?= esc($sharedItem->name) ?></h4>
                            <p class="text-muted">
                                <?= $sharedItem->human_size ?> • 
                                <?= tr("Shared by") ?> <?= esc($share->sharedBy->name) ?>
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title"><?= tr("File Information") ?></h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong><?= tr("Name") ?>:</strong> <?= esc($sharedItem->name) ?></li>
                                            <li><strong><?= tr("Size") ?>:</strong> <?= $sharedItem->human_size ?></li>
                                            <li><strong><?= tr("Type") ?>:</strong> <?= $sharedItem->mime_type ?></li>
                                            <li><strong><?= tr("Permission") ?>:</strong> 
                                                <span class="badge badge-<?= $share->permission_level === 'EDIT' ? 'warning' : 'info' ?>">
                                                    <?= $share->permission_label ?>
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title"><?= tr("Share Information") ?></h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong><?= tr("Shared by") ?>:</strong> <?= esc($share->sharedBy->name) ?></li>
                                            <li><strong><?= tr("Shared on") ?>:</strong> <?= $share->created_at->format('M d, Y') ?></li>
                                            <li><strong><?= tr("Access count") ?>:</strong> <?= $share->access_count ?></li>
                                            <li><strong><?= tr("Expires") ?>:</strong> <?= $share->expiration_status ?></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <div class="btn-group">
                                <?php if ($sharedItem->isDocument() || $sharedItem->isImage()): ?>
                                    <a href="<?= $sharedItem->getUrl() ?>" target="_blank" 
                                       class="btn btn-primary">
                                        <i class="fas fa-eye mr-1"></i><?= tr("View") ?>
                                    </a>
                                <?php endif; ?>
                                <a href="<?= $sharedItem->getDownloadUrl() ?>" 
                                   class="btn btn-success">
                                    <i class="fas fa-download mr-1"></i><?= tr("Download") ?>
                                </a>
                                <?php if ($sharedItem->canEditWithOnlyOffice() && $share->permission_level === 'EDIT'): ?>
                                    <button type="button" class="btn btn-warning" 
                                            onclick="editWithOnlyOffice('<?= $sharedItem->id ?>')">
                                        <i class="fas fa-edit mr-1"></i><?= tr("Edit") ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php elseif ($shareType === 'directory'): ?>
                        <!-- Directory Share -->
                        <div class="text-center mb-4">
                            <i class="fas fa-folder fa-4x text-warning mb-3"></i>
                            <h4><?= esc($sharedItem->name) ?></h4>
                            <p class="text-muted">
                                <?= tr("Shared by") ?> <?= esc($share->sharedBy->name) ?>
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title"><?= tr("Directory Information") ?></h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong><?= tr("Name") ?>:</strong> <?= esc($sharedItem->name) ?></li>
                                            <li><strong><?= tr("Files") ?>:</strong> <?= $sharedItem->files()->count() ?></li>
                                            <li><strong><?= tr("Subdirectories") ?>:</strong> <?= $sharedItem->children()->count() ?></li>
                                            <li><strong><?= tr("Permission") ?>:</strong> 
                                                <span class="badge badge-<?= $share->permission_level === 'EDIT' ? 'warning' : 'info' ?>">
                                                    <?= $share->permission_label ?>
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title"><?= tr("Share Information") ?></h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong><?= tr("Shared by") ?>:</strong> <?= esc($share->sharedBy->name) ?></li>
                                            <li><strong><?= tr("Shared on") ?>:</strong> <?= $share->created_at->format('M d, Y') ?></li>
                                            <li><strong><?= tr("Access count") ?>:</strong> <?= $share->access_count ?></li>
                                            <li><strong><?= tr("Expires") ?>:</strong> <?= $share->expiration_status ?></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Directory Contents -->
                        <?php $files = $sharedItem->files()->active()->get(); ?>
                        <?php if ($files->count() > 0): ?>
                            <div class="mt-4">
                                <h6><?= tr("Files in this directory") ?></h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th><?= tr("Name") ?></th>
                                                <th><?= tr("Size") ?></th>
                                                <th><?= tr("Actions") ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($files as $file): ?>
                                                <tr>
                                                    <td>
                                                        <i class="fas fa-file mr-1 text-info"></i>
                                                        <?= esc($file->name) ?>
                                                    </td>
                                                    <td><?= $file->human_size ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="<?= $file->getUrl() ?>" target="_blank" 
                                                               class="btn btn-outline-primary btn-sm">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="<?= $file->getDownloadUrl() ?>" 
                                                               class="btn btn-outline-success btn-sm">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>

                    <?php endif; ?>

                    <!-- Share Expired/Revoked Message -->
                    <?php if (!$share->isActive()): ?>
                        <div class="alert alert-warning mt-4">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <?php if ($share->revoked_at): ?>
                                <?= tr("This share has been revoked and is no longer accessible.") ?>
                            <?php elseif ($share->isExpired()): ?>
                                <?= tr("This share has expired and is no longer accessible.") ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer text-center text-muted">
                    <small>
                        <?= tr("Powered by") ?> <?= get_option('app_name', 'Drive System') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
function editWithOnlyOffice(fileId) {
    // OnlyOffice integration will be implemented
    alert('OnlyOffice integration coming soon!');
}
</script>
<?= $this->endSection() ?>
