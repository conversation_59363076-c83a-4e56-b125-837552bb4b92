<?php

namespace App\Controllers\Drive;

use App\Controllers\BaseController;
use App\Models\Drive\Directory;
use App\Models\Drive\File;
use App\Models\Drive\ActivityLog;
use App\Libraries\Storage;

class FileController extends BaseController
{
    /**
     * Upload files
     */
    public function upload()
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("Directory") => ["directory_id", "in_table:directories,id"],
            tr("File") => ["file", "required|uploaded_file:0,".get_option("storage_max_size").",".get_option("storage_extensions")]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()->all()
            ]);
        }

        $user = auth();
        $directoryId = input('directory_id');

        $directory_path = get_option("storage_path");
        

        if($directoryId ){
            // Validate directory
            $directory = Directory::find($directoryId);
            if (!$directory || !$directory->canAccess($user->id)) {
                return _response([
                    'success' => false,
                    'message' => ['Invalid directory or access denied']
                ]);
            }

            $directory_path = $directory->path;
        }

        

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            return _response([
                'success' => false,
                'message' => ['File upload failed']
            ]);
        }
        
        $file_id = uuid();

        $extension = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);

        // Create file record
        $file = new File([
            'id' => $file_id,
            'name' => $_FILES['file']['name'],
            'original_name' => $_FILES['file']['name'],
            'directory_id' => $directoryId,
            'user_id' => $user->id,
            'file_path' => $directory_path . "/" . $file_id.".".$extension,
            'mime_type' => $_FILES['file']['type'],
            'size_bytes' => $_FILES['file']['size'],
            
            'status' => File::STATUS_ACTIVE
        ]);

        // die($directory_path . "/" . $file_id);

        file_put_contents($directory_path . "/" . $file_id.".".$extension, file_get_contents($_FILES['file']['tmp_name']));

       

        

        $file->hash_sha256 = hash_file('sha256', $directory_path . "/" . $file_id.".".$extension);
        $file->save();

        // Update directory size

        if($directoryId){
            $directory->updateSize();
        }

        // Log activity
        ActivityLog::logFileUpload($file->id, $user->id, "Uploaded file: {$file->original_name}");

        return _response([
            'success' => true,
            'message' => ['File uploaded successfully'],
            'action' => 'reload',
            'data' => [
                'file' => $file
            ]
        ]);
    }


    public function create()
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("File name") => ["name", "required|max:250"],
            tr("File type") => ["type", "required|in:word,excel,powerpoint,file"],
            tr("Directory") => ["directory_id", "in_table:directories,id"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()->all()
            ]);
        }

        $user = auth();
        $fileName = input('name');
        $fileType = input('type');
        $directoryId = input('directory_id');

        $directory_path = get_option("storage_path");

        if ($directoryId) {
            // Validate directory
            $directory = Directory::find($directoryId);
            if (!$directory || !$directory->canAccess($user->id)) {
                return _response([
                    'success' => false,
                    'message' => ['Invalid directory or access denied']
                ]);
            }
            $directory_path = $directory->path;
        }

        // Determine file extension and mime type based on type
        $extensions = [
            'word' => 'docx',
            'excel' => 'xlsx', 
            'powerpoint' => 'pptx',
            'file' => 'txt'
        ];

        $mimeTypes = [
            'word' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'excel' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'powerpoint' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'file' => 'text/plain'
        ];

        $extension = $extensions[$fileType];
        $mimeType = $mimeTypes[$fileType];
        
        // Add extension to filename if not already present
        if (!str_ends_with(strtolower($fileName), '.' . $extension)) {
            $fileName .= '.' . $extension;
        }

        // Check if file with same name already exists in directory
        $existingFile = File::where('name', $fileName)
            ->where('directory_id', $directoryId)
            ->where('user_id', $user->id)
            ->first();

        if ($existingFile) {
            return _response([
                'success' => false,
                'message' => ['File with this name already exists in this directory']
            ]);
        }

        $file_id = uuid();
        $filePath = $directory_path . "/" . $file_id . "." . $extension;

        // Create the appropriate file content based on type
        $fileContent = $this->generateFileContent($fileType);
        
        // Write file to storage
        if (file_put_contents($filePath, $fileContent) === false) {
            return _response([
                'success' => false,
                'message' => ['Failed to create file']
            ]);
        }

        // Create file record
        $file = new File([
            'id' => $file_id,
            'name' => $fileName,
            'original_name' => $fileName,
            'directory_id' => $directoryId,
            'user_id' => $user->id,
            'file_path' => $filePath,
            'mime_type' => $mimeType,
            'size_bytes' => strlen($fileContent),
            'hash_sha256' => hash('sha256', $fileContent),
            'status' => File::STATUS_ACTIVE
        ]);

        $file->save();

        // Update directory size
        if ($directoryId) {
            $directory->updateSize();
        }

        // Log activity
        ActivityLog::logFileCreate($file->id, $user->id, "Created new {$fileType} file: {$fileName}");

        return _response([
            'success' => true,
            'message' => ['File created successfully'],
            'action' => 'reload',
            'data' => [
                'file' => $file
            ]
        ]);
    }

    /**
     * Generate basic file content based on file type
     */
    private function generateFileContent($fileType)
    {
        switch ($fileType) {
            case 'word':
                return $this->generateMinimalDocx();
            case 'excel':
                return $this->generateMinimalXlsx();
            case 'powerpoint':
                return $this->generateMinimalPptx();
            case 'file':
            default:
                return '';
        }
    }

    /**
     * Generate minimal DOCX file
     */
    private function generateMinimalDocx()
    {
        // Create a minimal DOCX structure using ZipArchive
        $zip = new \ZipArchive();
        $tempFile = tempnam(sys_get_temp_dir(), 'docx');
        
        if ($zip->open($tempFile, \ZipArchive::CREATE) !== TRUE) {
            return '';
        }

        // Add required DOCX structure
        $zip->addFromString('[Content_Types].xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
<Default Extension="xml" ContentType="application/xml"/>
<Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>');

        $zip->addFromString('_rels/.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>');

        $zip->addFromString('word/document.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
<w:body>
<w:p><w:r><w:t></w:t></w:r></w:p>
</w:body>
</w:document>');

        $zip->close();
        
        $content = file_get_contents($tempFile);
        unlink($tempFile);
        
        return $content;
    }

    /**
     * Generate minimal XLSX file
     */
    private function generateMinimalXlsx()
    {
        $zip = new \ZipArchive();
        $tempFile = tempnam(sys_get_temp_dir(), 'xlsx');
        
        if ($zip->open($tempFile, \ZipArchive::CREATE) !== TRUE) {
            return '';
        }

        $zip->addFromString('[Content_Types].xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
<Default Extension="xml" ContentType="application/xml"/>
<Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
<Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
</Types>');

        $zip->addFromString('_rels/.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>');

        $zip->addFromString('xl/workbook.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
<sheets>
<sheet name="Sheet1" sheetId="1" r:id="rId1"/>
</sheets>
</workbook>');

        $zip->addFromString('xl/_rels/workbook.xml.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
</Relationships>');

        $zip->addFromString('xl/worksheets/sheet1.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
<sheetData/>
</worksheet>');

        $zip->close();
        
        $content = file_get_contents($tempFile);
        unlink($tempFile);
        
        return $content;
    }

    /**
     * Generate minimal PPTX file
     */
    private function generateMinimalPptx()
    {
        $zip = new \ZipArchive();
        $tempFile = tempnam(sys_get_temp_dir(), 'pptx');
        
        if ($zip->open($tempFile, \ZipArchive::CREATE) !== TRUE) {
            return '';
        }

        // Content Types
        $zip->addFromString('[Content_Types].xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
<Default Extension="xml" ContentType="application/xml"/>
<Override PartName="/ppt/presentation.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml"/>
<Override PartName="/ppt/slides/slide1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slide+xml"/>
<Override PartName="/ppt/theme/theme1.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml"/>
<Override PartName="/ppt/slideLayouts/slideLayout1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/>
<Override PartName="/ppt/slideMasters/slideMaster1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml"/>
<Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml"/>
<Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"/>
</Types>');

        // Main relationships
        $zip->addFromString('_rels/.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="ppt/presentation.xml"/>
<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/>
<Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/>
</Relationships>');

        // Presentation
        $zip->addFromString('ppt/presentation.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:presentation xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main">
<p:sldMasterIdLst>
<p:sldMasterId id="2147483648" r:id="rId1"/>
</p:sldMasterIdLst>
<p:sldIdLst>
<p:sldId id="256" r:id="rId2"/>
</p:sldIdLst>
<p:sldSz cx="9144000" cy="6858000" type="screen4x3"/>
<p:notesSz cx="6858000" cy="9144000"/>
<p:defaultTextStyle>
<a:defPPr>
<a:defRPr lang="en-US"/>
</a:defPPr>
</p:defaultTextStyle>
</p:presentation>');

        // Presentation relationships
        $zip->addFromString('ppt/_rels/presentation.xml.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="slideMasters/slideMaster1.xml"/>
<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slides/slide1.xml"/>
</Relationships>');

        // Slide Master
        $zip->addFromString('ppt/slideMasters/slideMaster1.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldMaster xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main">
<p:cSld>
<p:bg>
<p:bgRef idx="1001">
<a:schemeClr val="bg1"/>
</p:bgRef>
</p:bg>
<p:spTree>
<p:nvGrpSpPr>
<p:cNvPr id="1" name=""/>
<p:cNvGrpSpPr/>
<p:nvPr/>
</p:nvGrpSpPr>
<p:grpSpPr>
<a:xfrm>
<a:off x="0" y="0"/>
<a:ext cx="0" cy="0"/>
<a:chOff x="0" y="0"/>
<a:chExt cx="0" cy="0"/>
</a:xfrm>
</p:grpSpPr>
</p:spTree>
</p:cSld>
<p:clrMap bg1="lt1" tx1="dk1" bg2="lt2" tx2="dk2" accent1="accent1" accent2="accent2" accent3="accent3" accent4="accent4" accent5="accent5" accent6="accent6" hlink="hlink" folHlink="folHlink"/>
<p:sldLayoutIdLst>
<p:sldLayoutId id="2147483649" r:id="rId1"/>
</p:sldLayoutIdLst>
<p:txStyles>
<p:titleStyle>
<a:lvl1pPr>
<a:defRPr/>
</a:lvl1pPr>
</p:titleStyle>
<p:bodyStyle>
<a:lvl1pPr>
<a:defRPr/>
</a:lvl1pPr>
</p:bodyStyle>
<p:otherStyle>
<a:lvl1pPr>
<a:defRPr/>
</a:lvl1pPr>
</p:otherStyle>
</p:txStyles>
</p:sldMaster>');

        // Slide Master relationships
        $zip->addFromString('ppt/slideMasters/_rels/slideMaster1.xml.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout1.xml"/>
<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="../theme/theme1.xml"/>
</Relationships>');

        // Slide Layout
        $zip->addFromString('ppt/slideLayouts/slideLayout1.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" type="blank" preserve="1">
<p:cSld name="Blank">
<p:spTree>
<p:nvGrpSpPr>
<p:cNvPr id="1" name=""/>
<p:cNvGrpSpPr/>
<p:nvPr/>
</p:nvGrpSpPr>
<p:grpSpPr>
<a:xfrm>
<a:off x="0" y="0"/>
<a:ext cx="0" cy="0"/>
<a:chOff x="0" y="0"/>
<a:chExt cx="0" cy="0"/>
</a:xfrm>
</p:grpSpPr>
</p:spTree>
</p:cSld>
<p:clrMapOvr>
<a:masterClrMapping/>
</p:clrMapOvr>
</p:sldLayout>');

        // Slide Layout relationships
        $zip->addFromString('ppt/slideLayouts/_rels/slideLayout1.xml.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="../slideMasters/slideMaster1.xml"/>
</Relationships>');

        // Slide
        $zip->addFromString('ppt/slides/slide1.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<p:sld xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main">
<p:cSld>
<p:spTree>
<p:nvGrpSpPr>
<p:cNvPr id="1" name=""/>
<p:cNvGrpSpPr/>
<p:nvPr/>
</p:nvGrpSpPr>
<p:grpSpPr>
<a:xfrm>
<a:off x="0" y="0"/>
<a:ext cx="0" cy="0"/>
<a:chOff x="0" y="0"/>
<a:chExt cx="0" cy="0"/>
</a:xfrm>
</p:grpSpPr>
</p:spTree>
</p:cSld>
<p:clrMapOvr>
<a:masterClrMapping/>
</p:clrMapOvr>
</p:sld>');

        // Slide relationships
        $zip->addFromString('ppt/slides/_rels/slide1.xml.rels', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout" Target="../slideLayouts/slideLayout1.xml"/>
</Relationships>');

        // Theme
        $zip->addFromString('ppt/theme/theme1.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">
<a:themeElements>
<a:clrScheme name="Office">
<a:dk1>
<a:sysClr val="windowText" lastClr="000000"/>
</a:dk1>
<a:lt1>
<a:sysClr val="window" lastClr="FFFFFF"/>
</a:lt1>
<a:dk2>
<a:srgbClr val="1F497D"/>
</a:dk2>
<a:lt2>
<a:srgbClr val="EEECE1"/>
</a:lt2>
<a:accent1>
<a:srgbClr val="4F81BD"/>
</a:accent1>
<a:accent2>
<a:srgbClr val="F79646"/>
</a:accent2>
<a:accent3>
<a:srgbClr val="9BBB59"/>
</a:accent3>
<a:accent4>
<a:srgbClr val="8064A2"/>
</a:accent4>
<a:accent5>
<a:srgbClr val="4BACC6"/>
</a:accent5>
<a:accent6>
<a:srgbClr val="F79646"/>
</a:accent6>
<a:hlink>
<a:srgbClr val="0000FF"/>
</a:hlink>
<a:folHlink>
<a:srgbClr val="800080"/>
</a:folHlink>
</a:clrScheme>
<a:fontScheme name="Office">
<a:majorFont>
<a:latin typeface="Calibri"/>
<a:ea typeface=""/>
<a:cs typeface=""/>
</a:majorFont>
<a:minorFont>
<a:latin typeface="Calibri"/>
<a:ea typeface=""/>
<a:cs typeface=""/>
</a:minorFont>
</a:fontScheme>
<a:fmtScheme name="Office">
<a:fillStyleLst>
<a:solidFill>
<a:schemeClr val="phClr"/>
</a:solidFill>
<a:gradFill rotWithShape="1">
<a:gsLst>
<a:gs pos="0">
<a:schemeClr val="phClr">
<a:tint val="50000"/>
<a:satMod val="300000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="35000">
<a:schemeClr val="phClr">
<a:tint val="37000"/>
<a:satMod val="300000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="100000">
<a:schemeClr val="phClr">
<a:tint val="15000"/>
<a:satMod val="350000"/>
</a:schemeClr>
</a:gs>
</a:gsLst>
<a:lin ang="16200000" scaled="1"/>
</a:gradFill>
<a:gradFill rotWithShape="1">
<a:gsLst>
<a:gs pos="0">
<a:schemeClr val="phClr">
<a:shade val="51000"/>
<a:satMod val="130000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="80000">
<a:schemeClr val="phClr">
<a:shade val="93000"/>
<a:satMod val="130000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="100000">
<a:schemeClr val="phClr">
<a:shade val="94000"/>
<a:satMod val="135000"/>
</a:schemeClr>
</a:gs>
</a:gsLst>
<a:lin ang="16200000" scaled="0"/>
</a:gradFill>
</a:fillStyleLst>
<a:lnStyleLst>
<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr">
<a:solidFill>
<a:schemeClr val="phClr">
<a:shade val="95000"/>
<a:satMod val="105000"/>
</a:schemeClr>
</a:solidFill>
<a:prstDash val="solid"/>
</a:ln>
<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr">
<a:solidFill>
<a:schemeClr val="phClr"/>
</a:solidFill>
<a:prstDash val="solid"/>
</a:ln>
<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr">
<a:solidFill>
<a:schemeClr val="phClr"/>
</a:solidFill>
<a:prstDash val="solid"/>
</a:ln>
</a:lnStyleLst>
<a:effectStyleLst>
<a:effectStyle>
<a:effectLst>
<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0">
<a:srgbClr val="000000">
<a:alpha val="38000"/>
</a:srgbClr>
</a:outerShdw>
</a:effectLst>
</a:effectStyle>
<a:effectStyle>
<a:effectLst>
<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
<a:srgbClr val="000000">
<a:alpha val="35000"/>
</a:srgbClr>
</a:outerShdw>
</a:effectLst>
</a:effectStyle>
<a:effectStyle>
<a:effectLst>
<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
<a:srgbClr val="000000">
<a:alpha val="35000"/>
</a:srgbClr>
</a:outerShdw>
</a:effectLst>
<a:scene3d>
<a:camera prst="orthographicFront">
<a:rot lat="0" lon="0" rev="0"/>
</a:camera>
<a:lightRig rig="threePt" dir="t">
<a:rot lat="0" lon="0" rev="1200000"/>
</a:lightRig>
</a:scene3d>
<a:sp3d>
<a:bevelT w="63500" h="25400"/>
</a:sp3d>
</a:effectStyle>
</a:effectStyleLst>
<a:bgFillStyleLst>
<a:solidFill>
<a:schemeClr val="phClr"/>
</a:solidFill>
<a:gradFill rotWithShape="1">
<a:gsLst>
<a:gs pos="0">
<a:schemeClr val="phClr">
<a:tint val="40000"/>
<a:satMod val="350000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="40000">
<a:schemeClr val="phClr">
<a:tint val="45000"/>
<a:shade val="99000"/>
<a:satMod val="350000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="100000">
<a:schemeClr val="phClr">
<a:shade val="20000"/>
<a:satMod val="255000"/>
</a:schemeClr>
</a:gs>
</a:gsLst>
<a:path path="circle">
<a:fillToRect l="50000" t="-80000" r="50000" b="180000"/>
</a:path>
</a:gradFill>
<a:gradFill rotWithShape="1">
<a:gsLst>
<a:gs pos="0">
<a:schemeClr val="phClr">
<a:tint val="80000"/>
<a:satMod val="300000"/>
</a:schemeClr>
</a:gs>
<a:gs pos="100000">
<a:schemeClr val="phClr">
<a:shade val="30000"/>
<a:satMod val="200000"/>
</a:schemeClr>
</a:gs>
</a:gsLst>
<a:path path="circle">
<a:fillToRect l="50000" t="50000" r="50000" b="50000"/>
</a:path>
</a:gradFill>
</a:bgFillStyleLst>
</a:fmtScheme>
</a:themeElements>
<a:objectDefaults/>
<a:extraClrSchemeLst/>
</a:theme>');

        // Core properties
        $zip->addFromString('docProps/core.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
<dc:title></dc:title>
<dc:creator></dc:creator>
<cp:lastModifiedBy></cp:lastModifiedBy>
<cp:revision>1</cp:revision>
<dcterms:created xsi:type="dcterms:W3CDTF">' . date('Y-m-d\TH:i:s\Z') . '</dcterms:created>
<dcterms:modified xsi:type="dcterms:W3CDTF">' . date('Y-m-d\TH:i:s\Z') . '</dcterms:modified>
</cp:coreProperties>');

        // App properties
        $zip->addFromString('docProps/app.xml', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
<Application>Microsoft Office PowerPoint</Application>
<PresentationFormat>Widescreen</PresentationFormat>
<Paragraphs>0</Paragraphs>
<Slides>1</Slides>
<Notes>0</Notes>
<HiddenSlides>0</HiddenSlides>
<MMClips>0</MMClips>
<ScaleCrop>false</ScaleCrop>
<LinksUpToDate>false</LinksUpToDate>
<SharedDoc>false</SharedDoc>
<HyperlinksChanged>false</HyperlinksChanged>
<AppVersion>16.0000</AppVersion>
</Properties>');

        $zip->close();
        
        $content = file_get_contents($tempFile);
        unlink($tempFile);
        
        return $content;
    }

    /**
     * Download a file
     */
    public function download($fileId)
    {
        if (!auth()) {
            return _error_code(401);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || !$file->canAccess($user->id)) {
            return _error_code(404);
        }

        // Increment download count
        $file->incrementDownloadCount();

        // Log activity
        ActivityLog::logFileDownload($file->id, $user->id, "Downloaded file: {$file->name}");

        // Use storage library to serve file
        $storage = new Storage();
        $fileContent = $storage->read($file->file_path);

        if ($fileContent === false) {
            return _error_code(404);
        }

        // Set headers for download
        $this->response->setHeader('Content-Type', $file->mime_type);
        $this->response->setHeader('Content-Length', strlen($fileContent));
        $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $file->name . '"');
        $this->response->setBody($fileContent);

        return $this->response;
    }

    /**
     * View/preview a file
     */
    public function view($fileId)
    {
        if (!auth()) {
            return _error_code(401);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || !$file->canAccess($user->id)) {
            return _error_code(404);
        }

        // Log activity
        ActivityLog::logFileView($file->id, $user->id, "Viewed file: {$file->name}");

        // Use storage library to serve file
        $storage = new Storage();
        $fileContent = $storage->read($file->file_path);

        if ($fileContent === false) {
            return _error_code(404);
        }

        // Set headers for inline viewing
        $this->response->setHeader('Content-Type', $file->mime_type);
        $this->response->setHeader('Content-Length', strlen($fileContent));
        $this->response->setHeader('Content-Disposition', 'inline; filename="' . $file->name . '"');
        $this->response->setBody($fileContent);

        return $this->response;
    }

    /**
     * Delete a file
     */
    public function delete($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || $file->user_id != $user->id) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        $fileName = $file->name;
        $directory = $file->directory;

        // Soft delete the file
        $file->delete();

        // Update directory size
        if ($directory) {
            $directory->updateSize();
        }

        // Log activity
        ActivityLog::logActivity('DELETE', $user->id, $file->id, null, "Deleted file: {$fileName}");

        return _response([
            'success' => true,
            'message' => ['File deleted successfully']
        ]);
    }

    /**
     * Rename a file
     */
    public function rename($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("File name") => ["name", "required|max_length[255]"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || $file->user_id != $user->id) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        $newName = input('name');
        $oldName = $file->name;

        // Check if file with same name already exists in directory
        $existingFile = File::where('name', $newName)
            ->where('directory_id', $file->directory_id)
            ->where('user_id', $user->id)
            ->where('id', '!=', $fileId)
            ->first();

        if ($existingFile) {
            return _response([
                'success' => false,
                'message' => ['File with this name already exists in this directory']
            ]);
        }

        // Update file name
        $file->name = $newName;
        $file->save();

        // Log activity
        ActivityLog::logActivity('RENAME', $user->id, $file->id, null, "Renamed file from '{$oldName}' to '{$newName}'");

        return _response([
            'success' => true,
            'message' => ['File renamed successfully'],
            'data' => [
                'file' => $file
            ]
        ]);
    }

    /**
     * Move a file to another directory
     */
    public function move($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("Target directory") => ["target_directory_id", "required"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        $user = auth();
        $file = File::find($fileId);
        $targetDirectoryId = input('target_directory_id');

        if (!$file || $file->user_id != $user->id) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        // Validate target directory
        $targetDirectory = Directory::find($targetDirectoryId);
        if (!$targetDirectory || !$targetDirectory->canAccess($user->id)) {
            return _response([
                'success' => false,
                'message' => ['Invalid target directory']
            ]);
        }

        // Check if file with same name already exists in target directory
        $existingFile = File::where('name', $file->name)
            ->where('directory_id', $targetDirectoryId)
            ->where('user_id', $user->id)
            ->where('id', '!=', $fileId)
            ->first();

        if ($existingFile) {
            return _response([
                'success' => false,
                'message' => ['File with this name already exists in target directory']
            ]);
        }

        // Move file
        $operation = $file->moveTo($targetDirectoryId, $user->id);

        // Log activity
        ActivityLog::logActivity('MOVE', $user->id, $file->id, null, "Moved file to: {$targetDirectory->name}");

        return _response([
            'success' => true,
            'message' => ['File moved successfully'],
            'data' => [
                'file' => $file,
                'operation' => $operation
            ]
        ]);
    }

    /**
     * Copy a file to another directory
     */
    public function copy($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("Target directory") => ["target_directory_id", "required"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        $user = auth();
        $file = File::find($fileId);
        $targetDirectoryId = input('target_directory_id');

        if (!$file || !$file->canAccess($user->id)) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        // Validate target directory
        $targetDirectory = Directory::find($targetDirectoryId);
        if (!$targetDirectory || !$targetDirectory->canAccess($user->id)) {
            return _response([
                'success' => false,
                'message' => ['Invalid target directory']
            ]);
        }

        // Generate new name if file exists in target
        $newName = $file->name;
        $counter = 1;
        while (File::where('name', $newName)
                   ->where('directory_id', $targetDirectoryId)
                   ->where('user_id', $user->id)
                   ->exists()) {
            $pathInfo = pathinfo($file->name);
            $baseName = $pathInfo['filename'];
            $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
            $newName = $baseName . " (Copy {$counter})" . $extension;
            $counter++;
        }

        // Create copy
        $copiedFile = new File([
            'name' => $newName,
            'original_name' => $file->original_name,
            'directory_id' => $targetDirectoryId,
            'user_id' => $user->id,
            'file_path' => $file->file_path, // Same physical file
            'mime_type' => $file->mime_type,
            'size_bytes' => $file->size_bytes,
            'hash_sha256' => $file->hash_sha256,
            'status' => File::STATUS_ACTIVE
        ]);

        $copiedFile->save();

        // Update target directory size
        $targetDirectory->updateSize();

        // Log activity
        ActivityLog::logActivity('COPY', $user->id, $copiedFile->id, null, "Copied file to: {$targetDirectory->name}");

        return _response([
            'success' => true,
            'message' => ['File copied successfully'],
            'data' => [
                'file' => $copiedFile
            ]
        ]);
    }
}
